2025-08-28 19:08:12 | INFO | testcases.test_ella.base_ella_test:setup_batch_test_screen:68 | 🚀 开始批量测试屏幕设置...
2025-08-28 19:08:12 | INFO | utils.screen_manager:set_screen_never_timeout:129 | 设置屏幕永不超时
2025-08-28 19:08:12 | INFO | utils.screen_manager:set_screen_timeout:95 | 设置屏幕超时时间为: 2147483647ms (2147483.647秒)
2025-08-28 19:08:12 | INFO | utils.screen_manager:get_current_screen_timeout:60 | 获取当前屏幕超时时间
2025-08-28 19:08:12 | INFO | utils.screen_manager:get_current_screen_timeout:68 | 当前屏幕超时时间: 2147483647ms (2147483.647秒)
2025-08-28 19:08:13 | INFO | utils.screen_manager:get_current_screen_timeout:60 | 获取当前屏幕超时时间
2025-08-28 19:08:13 | INFO | utils.screen_manager:get_current_screen_timeout:68 | 当前屏幕超时时间: 2147483647ms (2147483.647秒)
2025-08-28 19:08:13 | INFO | utils.screen_manager:set_screen_timeout:109 | 屏幕超时时间设置成功: 2147483647ms
2025-08-28 19:08:13 | INFO | utils.screen_manager:enable_stay_on_while_plugged:248 | 启用充电时保持屏幕常亮(所有充电方式)
2025-08-28 19:08:13 | INFO | utils.screen_manager:set_stay_on_while_plugged:214 | 设置充电时保持屏幕常亮: 所有充电方式都保持常亮
2025-08-28 19:08:13 | INFO | utils.screen_manager:get_stay_on_while_plugged_setting:159 | 获取充电时保持屏幕常亮设置
2025-08-28 19:08:13 | INFO | utils.screen_manager:get_stay_on_while_plugged_setting:175 | 充电时保持屏幕常亮设置: 所有充电方式都保持常亮 (值: 7)
2025-08-28 19:08:14 | INFO | utils.screen_manager:get_stay_on_while_plugged_setting:159 | 获取充电时保持屏幕常亮设置
2025-08-28 19:08:14 | INFO | utils.screen_manager:get_stay_on_while_plugged_setting:175 | 充电时保持屏幕常亮设置: 所有充电方式都保持常亮 (值: 7)
2025-08-28 19:08:14 | INFO | utils.screen_manager:set_stay_on_while_plugged:228 | 充电时保持屏幕常亮设置成功: 所有充电方式都保持常亮
2025-08-28 19:08:14 | INFO | testcases.test_ella.base_ella_test:setup_batch_test_screen:80 | ✅ 批量测试屏幕设置完成：屏幕永不超时，充电时保持常亮
2025-08-28 19:08:14 | INFO | utils.screen_manager:get_screen_status_info:343 | 获取屏幕状态详细信息
2025-08-28 19:08:14 | INFO | utils.screen_manager:get_current_screen_timeout:60 | 获取当前屏幕超时时间
2025-08-28 19:08:14 | INFO | utils.screen_manager:get_current_screen_timeout:68 | 当前屏幕超时时间: 2147483647ms (2147483.647秒)
2025-08-28 19:08:14 | INFO | utils.screen_manager:get_stay_on_while_plugged_setting:159 | 获取充电时保持屏幕常亮设置
2025-08-28 19:08:14 | INFO | utils.screen_manager:get_stay_on_while_plugged_setting:175 | 充电时保持屏幕常亮设置: 所有充电方式都保持常亮 (值: 7)
2025-08-28 19:08:14 | INFO | testcases.test_ella.base_ella_test:setup_batch_test_screen:84 | 📊 当前屏幕状态: 超时=永不超时, 充电常亮=所有充电方式都保持常亮
2025-08-28 19:08:14 | INFO | core.base_page:__init__:38 | 初始化页面: ella - dialogue_page
2025-08-28 19:08:15 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:299 | 🧹 开始清除手机上所有运行中的应用进程...
2025-08-28 19:08:15 | INFO | tools.adb_process_monitor:clear_all_running_processes:1825 | 🧹 开始清除手机上所有运行中的应用进程...
2025-08-28 19:08:15 | INFO | tools.adb_process_monitor:clear_all_running_processes:1841 | ⚡ 优先使用命令直接清理...
2025-08-28 19:08:16 | INFO | tools.adb_process_monitor:clear_all_running_processes:1847 | 💪 强制停止顽固应用...
2025-08-28 19:08:21 | INFO | tools.adb_process_monitor:clear_all_running_processes:1857 | 🎉 应用进程清理完成，共清理 31 个应用
2025-08-28 19:08:23 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:301 | ✅ 应用进程清理完成
2025-08-28 19:08:23 | INFO | pages.apps.ella.dialogue_page:start_app:215 | 启动Ella应用
2025-08-28 19:08:23 | INFO | pages.apps.ella.dialogue_page:start_app:223 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-28 19:08:26 | INFO | pages.apps.ella.dialogue_page:_check_app_started:281 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-08-28 19:08:26 | INFO | pages.apps.ella.dialogue_page:start_app:228 | ✅ Ella应用启动成功（指定Activity）
2025-08-28 19:08:26 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:302 | 等待Ella页面加载完成 (超时: 15秒)
2025-08-28 19:08:27 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:312 | ✅ 确认当前在Ella应用中
2025-08-28 19:08:27 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:321 | ✅ 主输入框已出现，页面加载完成
2025-08-28 19:08:27 | INFO | testcases.test_ella.base_ella_test:ella_app:333 | ✅ Ella应用启动成功
2025-08-28 19:08:27 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:371 | 初始状态None- 使用命令who is j k rowling，状态: 
2025-08-28 19:08:27 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:770 | 确保在对话页面...
2025-08-28 19:08:27 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-08-28 19:08:27 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-08-28 19:08:27 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-28 19:08:27 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-08-28 19:08:27 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:781 | ✅ 已在对话页面
2025-08-28 19:08:27 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-08-28 19:08:28 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-08-28 19:08:28 | INFO | testcases.test_ella.base_ella_test:_execute_command:884 | 🎤 执行语音命令: who is j k rowling (语言: en-US, 持续时间: 3.0秒)
2025-08-28 19:08:28 | INFO | pages.apps.ella.ella_command_executor:execute_voice_command:73 | 🎤 执行语音命令: 'who is j k rowling' (语言: en-US, 持续时间: 3.0秒)
2025-08-28 19:08:28 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-08-28 19:08:28 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-08-28 19:08:28 | INFO | pages.apps.ella.ella_command_executor:_start_voice_input:342 | 🎤 启动语音输入...
2025-08-28 19:08:28 | INFO | pages.apps.ella.ella_command_executor:_try_click_voice_button_strategies:364 | 尝试多种策略点击语音按钮...
2025-08-28 19:08:28 | INFO | pages.apps.ella.ella_command_executor:_try_primary_voice_button:397 | ✅ 找到主要语音按钮，尝试点击
2025-08-28 19:08:28 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音输入按钮], 超时时间: 5秒
2025-08-28 19:08:28 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-28 19:08:28 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音输入按钮]
2025-08-28 19:08:28 | INFO | core.base_element:click:231 | 点击元素成功 [语音输入按钮]
2025-08-28 19:08:29 | INFO | pages.apps.ella.ella_command_executor:_verify_voice_input_started:541 | 验证语音输入启动状态...
2025-08-28 19:08:31 | INFO | pages.apps.ella.ella_command_executor:_verify_voice_input_started:553 | ✅ 检测到语音按钮状态变化
2025-08-28 19:08:31 | INFO | pages.apps.ella.ella_command_executor:_start_voice_input:348 | ✅ 语音输入启动成功
2025-08-28 19:08:31 | INFO | pages.apps.ella.ella_command_executor:execute_voice_command:90 | 等待语音录制状态稳定...
2025-08-28 19:08:32 | INFO | pages.apps.ella.ella_command_executor:execute_voice_command:94 | 🔊 播放语音命令: 'who is j k rowling'
2025-08-28 19:08:32 | INFO | pages.apps.ella.ella_command_executor:play_voice_command_file:837 | 🎵 播放命令语音: 'who is j k rowling' (语言: en-US)
2025-08-28 19:08:32 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-08-28 19:08:32 | INFO | pages.apps.ella.ella_command_executor:play_voice_command_file:857 | ✅ 找到现有音频文件: data\tts\en\who_is_j_k_rowling.wav
2025-08-28 19:08:32 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-08-28 19:08:32 | INFO | pages.apps.ella.ella_command_executor:_play_with_windows_player:911 | 🔊 使用Windows播放器播放: who_is_j_k_rowling.wav (14.5KB)
2025-08-28 19:08:33 | INFO | pages.apps.ella.ella_command_executor:_play_with_windows_player:926 | ✅ Windows播放器播放完成
2025-08-28 19:08:36 | INFO | pages.apps.ella.ella_command_executor:_stop_voice_input:606 | 🛑 停止语音输入...
2025-08-28 19:08:36 | INFO | pages.apps.ella.ella_command_executor:_try_stop_voice_input_strategies:628 | 尝试多种策略停止语音输入...
2025-08-28 19:08:37 | INFO | pages.apps.ella.ella_command_executor:_try_stop_by_coordinates:746 | 尝试点击坐标 (972, 2192) 停止语音输入
2025-08-28 19:08:39 | INFO | pages.apps.ella.ella_command_executor:_verify_voice_input_stopped:776 | 验证语音输入停止状态...
2025-08-28 19:08:40 | INFO | pages.apps.ella.ella_command_executor:_verify_voice_input_stopped:783 | ✅ 录音指示器已消失
2025-08-28 19:08:40 | INFO | pages.apps.ella.ella_command_executor:_stop_voice_input:612 | ✅ 语音输入停止成功
2025-08-28 19:08:40 | INFO | pages.apps.ella.ella_command_executor:execute_voice_command:110 | ✅ 语音命令执行完成
2025-08-28 19:08:40 | INFO | testcases.test_ella.base_ella_test:_execute_command:891 | ✅ 成功执行命令: who is j k rowling (语音模式: True)
2025-08-28 19:08:40 | INFO | testcases.test_ella.base_ella_test:_handle_popup_after_command:178 | handle_popup_after_command:处理弹窗
2025-08-28 19:08:40 | INFO | core.popup_tool:detect_and_close_popup_once:738 | 执行单次弹窗检测和关闭
2025-08-28 19:08:47 | INFO | core.popup_tool:detect_and_close_popup_once:742 | 未检测到弹窗，无需处理
2025-08-28 19:08:47 | INFO | testcases.test_ella.base_ella_test:_get_response_timeout:1320 | 📝 使用默认等待时间: 8秒
2025-08-28 19:08:47 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:35 | 等待AI响应，超时时间: 8秒
2025-08-28 19:08:48 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:60 | ✅ 通过TTS按钮检测到响应
2025-08-28 19:08:51 | INFO | testcases.test_ella.base_ella_test:_get_final_status_with_page_info:445 | 状态检查时当前应用包名: com.transsion.aivoiceassistant
2025-08-28 19:08:51 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:563 | 状态检查完成，现在获取响应文本
2025-08-28 19:08:51 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:567 | 第1次尝试确保在Ella页面以获取响应
2025-08-28 19:08:51 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:770 | 确保在对话页面...
2025-08-28 19:08:51 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-08-28 19:08:51 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-08-28 19:08:51 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-28 19:08:51 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-08-28 19:08:52 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:781 | ✅ 已在对话页面
2025-08-28 19:08:52 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:571 | ✅ 已确认在Ella对话页面，可以获取响应
2025-08-28 19:08:52 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-08-28 19:08:52 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-08-28 19:08:52 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-28 19:08:52 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-08-28 19:08:52 | INFO | pages.apps.ella.ella_response_handler:_ensure_on_ella_page:1335 | 检查是否在Ella页面...
2025-08-28 19:08:52 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-08-28 19:08:52 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-08-28 19:08:52 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-28 19:08:52 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-08-28 19:08:52 | INFO | pages.apps.ella.ella_response_handler:_ensure_on_ella_page:1344 | ✅ 当前在Ella页面
2025-08-28 19:08:52 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:140 | 获取AI响应文本
2025-08-28 19:08:53 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:474 | ✅ 从asr_txt成功获取响应: Who is jk rollings
2025-08-28 19:08:54 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | robot_text节点不存在，已达到最大重试次数
2025-08-28 19:08:55 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | function_name节点不存在，已达到最大重试次数
2025-08-28 19:08:56 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | function_control节点不存在，已达到最大重试次数
2025-08-28 19:08:57 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:474 | ✅ 从tv_card_chat_gpt成功获取响应: J.K. Rowling, born Joanne Rowling on July 31, 1965, is a British author and philanthropist 1456. She is best known as the creator of the Harry Potter series, a seven-volume series about a young wizard 2. The "K" in her pen name stands for Kathleen, her paternal grandmother's name 3.
2025-08-28 19:08:57 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:474 | ✅ 从tv_top成功获取响应: Generated by AI, for reference only
2025-08-28 19:08:58 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | tv_banner节点不存在，已达到最大重试次数
2025-08-28 19:08:59 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | tv_text节点不存在，已达到最大重试次数
2025-08-28 19:09:01 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | alarm_time_tv节点不存在，已达到最大重试次数
2025-08-28 19:09:02 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | alarm_day_tv节点不存在，已达到最大重试次数
2025-08-28 19:09:03 | WARNING | pages.apps.ella.ella_response_handler:_get_element_checked_with_retry:530 | alarm_switch节点不存在，已达到最大重试次数
2025-08-28 19:09:03 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:168 | 尝试获取其他有效的响应文本
2025-08-28 19:09:03 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:1049 | 从TextView元素获取响应
2025-08-28 19:09:03 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:1054 | 从TextView获取响应: Dialogue
2025-08-28 19:09:03 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:180 | ✅ 获取到响应文本: Dialogue
2025-08-28 19:09:03 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_chat_list:1065 | 查找RecyclerView中的最新消息
2025-08-28 19:09:04 | INFO | pages.apps.ella.ella_response_handler:_extract_text_from_check_area_dump:897 | 从dump正则提取文本: Dialogue Explore Swipe down to view earlier chats Swift & Kelce: Family Ties Grow Who is jk rollings 6 Reference materials Generated by AI, for reference only J.K. Rowling's early life and influences Rowling's other literary works Harry Potter series' global impact DeepSeek-R1 Feel free to ask me any questions… 7:09
2025-08-28 19:09:04 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:180 | ✅ 获取到响应文本: Dialogue Explore Swipe down to view earlier chats Swift & Kelce: Family Ties Grow Who is jk rollings 6 Reference materials Generated by AI, for reference only J.K. Rowling's early life and influences Rowling's other literary works Harry Potter series' global impact DeepSeek-R1 Feel free to ask me any questions… 7:09
2025-08-28 19:09:04 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:185 | 未获取到有效的响应文本
2025-08-28 19:09:04 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:601 | 最终获取的AI响应: '['Who is jk rollings', '', '', '', 'J.K. Rowling, born Joanne Rowling on July 31, 1965, is a British author and philanthropist 1456. She is best known as the creator of the Harry Potter series, a seven-volume series about a young wizard 2. The "K" in her pen name stands for Kathleen, her paternal grandmother\'s name 3.', 'Generated by AI, for reference only', '', '', '', '', '', 'Dialogue', "Dialogue Explore Swipe down to view earlier chats Swift & Kelce: Family Ties Grow Who is jk rollings 6 Reference materials Generated by AI, for reference only J.K. Rowling's early life and influences Rowling's other literary works Harry Potter series' global impact DeepSeek-R1 Feel free to ask me any questions… 7:09"]'
2025-08-28 19:09:04 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaWhoIsJKRowlingVoice\test_completed.png
2025-08-28 19:09:04 | INFO | testcases.test_ella.base_ella_test:simple_command_test:1401 | 🎉 who is j k rowling 测试完成
2025-08-28 19:09:04 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:1031 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['Who is jk rollings', '', '', '', 'J.K. Rowling, born Joanne Rowling on July 31, 1965, is a British author and philanthropist 1456. She is best known as the creator of the Harry Potter series, a seven-volume series about a young wizard 2. The "K" in her pen name stands for Kathleen, her paternal grandmother\'s name 3.', 'Generated by AI, for reference only', '', '', '', '', '', 'Dialogue', "Dialogue Explore Swipe down to view earlier chats Swift & Kelce: Family Ties Grow Who is jk rollings 6 Reference materials Generated by AI, for reference only J.K. Rowling's early life and influences Rowling's other literary works Harry Potter series' global impact DeepSeek-R1 Feel free to ask me any questions… 7:09"]
2025-08-28 19:09:04 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1078 | ⚠️ 响应未包含期望内容: ' j k rowling'
2025-08-28 19:09:04 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1085 | ❌ 部分期望内容未找到 (0/1)
2025-08-28 19:09:04 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1086 | 缺失内容: [' j k rowling']
2025-08-28 19:09:04 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1087 | 搜索文本: 'Who is jk rollings J.K. Rowling, born Joanne Rowling on July 31, 1965, is a British author and philanthropist 1456. She is best known as the creator of the Harry Potter series, a seven-volume series about a young wizard 2. The "K" in her pen name stands for Kathleen, her paternal grandmother's name 3. Generated by AI, for reference only Dialogue Dialogue Explore Swipe down to view earlier chats Swift & Kelce: Family Ties Grow Who is jk rollings 6 Reference materials Generated by AI, for reference only J.K. Rowling's early life and influences Rowling's other literary works Harry Potter series' global impact DeepSeek-R1 Feel free to ask me any questions… 7:09'
2025-08-28 19:09:04 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1089 | 响应未包含期望内容: '[' j k rowling']'
2025-08-28 19:09:05 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaWhoIsJKRowlingVoice\failure_test_who_is_j_k_rowling_voice_20250828_190904.png
2025-08-28 19:09:05 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaWhoIsJKRowlingVoice\failure_test_who_is_j_k_rowling_voice_20250828_190904.png
2025-08-28 19:09:05 | INFO | pages.apps.ella.dialogue_page:stop_app:389 | 停止Ella应用
2025-08-28 19:09:06 | INFO | pages.apps.ella.dialogue_page:stop_app:400 | ✅ Ella应用已成功停止
