{"name": "测试who is j k rowling能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含[' j k rowling']，实际响应: '['Who is jk rollings', '', '', '', '<PERSON><PERSON><PERSON><PERSON>, born <PERSON> on July 31, 1965, is a British author and philanthropist 1456. She is best known as the creator of the <PERSON> series, a seven-volume series about a young wizard 2. The \"K\" in her pen name stands for <PERSON>, her paternal grandmother\\'s name 3.', 'Generated by AI, for reference only', '', '', '', '', '', 'Dialogue', \"Dialogue Explore Swipe down to view earlier chats Swift & Kelce: Family Ties Grow Who is jk rollings 6 Reference materials Generated by AI, for reference only <PERSON><PERSON><PERSON><PERSON>'s early life and influences <PERSON><PERSON>'s other literary works <PERSON> series' global impact DeepSeek-R1 Feel free to ask me any questions… 7:09\"]'\nassert False", "trace": "self = <test_voice_who_is_j_k_rowling.TestEllaWhoIsJKRowlingVoice object at 0x000001ED89C0F6D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001ED89FF8150>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_who_is_j_k_rowling_voice(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        voice_language = self.voice_language\n        voice_duration = self.voice_duration\n    \n        with allure.step(f\"执行语音命令: {command} (语言: {voice_language})\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command,\n                is_voice=True,  # 语音标识为True\n                voice_duration=voice_duration,\n                voice_language=voice_language, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含[' j k rowling']，实际响应: '['Who is jk rollings', '', '', '', 'J.K. Rowling, born Joanne Rowling on July 31, 1965, is a British author and philanthropist 1456. She is best known as the creator of the Harry Potter series, a seven-volume series about a young wizard 2. The \"K\" in her pen name stands for Kathleen, her paternal grandmother\\'s name 3.', 'Generated by AI, for reference only', '', '', '', '', '', 'Dialogue', \"Dialogue Explore Swipe down to view earlier chats Swift & Kelce: Family Ties Grow Who is jk rollings 6 Reference materials Generated by AI, for reference only J.K. Rowling's early life and influences Rowling's other literary works Harry Potter series' global impact DeepSeek-R1 Feel free to ask me any questions… 7:09\"]'\nE           assert False\n\ntestcases\\test_voice_ella\\dialogue\\test_voice_who_is_j_k_rowling.py:40: AssertionError"}, "description": "who is j k rowling", "steps": [{"name": "执行语音命令: who is j k rowling (语言: en-US)", "status": "passed", "steps": [{"name": "执行命令: who is j k rowling", "status": "passed", "start": 1756379307401, "stop": 1756379344481}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1fb23d21-ff46-47a2-bece-99b75210d55c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "bab7fe17-20fa-49e0-b86a-5c741b1b7f9d-attachment.png", "type": "image/png"}], "start": 1756379344481, "stop": 1756379344887}], "start": 1756379307401, "stop": 1756379344887}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含[' j k rowling']，实际响应: '['Who is jk rollings', '', '', '', '<PERSON><PERSON><PERSON><PERSON>, born <PERSON> on July 31, 1965, is a British author and philanthropist 1456. She is best known as the creator of the <PERSON> series, a seven-volume series about a young wizard 2. The \"K\" in her pen name stands for <PERSON>, her paternal grandmother\\'s name 3.', 'Generated by AI, for reference only', '', '', '', '', '', 'Dialogue', \"Dialogue Explore Swipe down to view earlier chats Swift & Kelce: Family Ties Grow Who is jk rollings 6 Reference materials Generated by AI, for reference only <PERSON><PERSON><PERSON><PERSON>'s early life and influences <PERSON><PERSON>'s other literary works <PERSON> series' global impact DeepSeek-R1 Feel free to ask me any questions… 7:09\"]'\nassert False\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_voice_ella\\dialogue\\test_voice_who_is_j_k_rowling.py\", line 40, in test_who_is_j_k_rowling_voice\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756379344887, "stop": 1756379344896}], "attachments": [{"name": "stdout", "source": "304c4e10-1246-44b4-bb94-26e642d9ab3a-attachment.txt", "type": "text/plain"}], "start": 1756379307401, "stop": 1756379344898, "uuid": "288f7218-3d98-492f-8ec1-8b5201f7f28c", "historyId": "b14e502d25d09860e1d85c778106d909", "testCaseId": "b14e502d25d09860e1d85c778106d909", "fullName": "testcases.test_voice_ella.dialogue.test_voice_who_is_j_k_rowling.TestEllaWhoIsJKRowlingVoice#test_who_is_j_k_rowling_voice", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_voice_ella.dialogue"}, {"name": "suite", "value": "test_voice_who_is_j_k_rowling"}, {"name": "subSuite", "value": "TestEllaWhoIsJKRowlingVoice"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "26792-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_voice_ella.dialogue.test_voice_who_is_j_k_rowling"}]}