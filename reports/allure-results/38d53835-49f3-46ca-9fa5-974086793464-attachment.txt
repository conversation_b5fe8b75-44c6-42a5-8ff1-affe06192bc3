2025-08-28 19:09:06 | INFO | testcases.test_ella.base_ella_test:setup_batch_test_screen:68 | 🚀 开始批量测试屏幕设置...
2025-08-28 19:09:06 | INFO | utils.screen_manager:set_screen_never_timeout:129 | 设置屏幕永不超时
2025-08-28 19:09:06 | INFO | utils.screen_manager:set_screen_timeout:95 | 设置屏幕超时时间为: 2147483647ms (2147483.647秒)
2025-08-28 19:09:06 | INFO | utils.screen_manager:get_current_screen_timeout:60 | 获取当前屏幕超时时间
2025-08-28 19:09:06 | INFO | utils.screen_manager:get_current_screen_timeout:68 | 当前屏幕超时时间: 2147483647ms (2147483.647秒)
2025-08-28 19:09:07 | INFO | utils.screen_manager:get_current_screen_timeout:60 | 获取当前屏幕超时时间
2025-08-28 19:09:08 | INFO | utils.screen_manager:get_current_screen_timeout:68 | 当前屏幕超时时间: 2147483647ms (2147483.647秒)
2025-08-28 19:09:08 | INFO | utils.screen_manager:set_screen_timeout:109 | 屏幕超时时间设置成功: 2147483647ms
2025-08-28 19:09:08 | INFO | utils.screen_manager:enable_stay_on_while_plugged:248 | 启用充电时保持屏幕常亮(所有充电方式)
2025-08-28 19:09:08 | INFO | utils.screen_manager:set_stay_on_while_plugged:214 | 设置充电时保持屏幕常亮: 所有充电方式都保持常亮
2025-08-28 19:09:08 | INFO | utils.screen_manager:get_stay_on_while_plugged_setting:159 | 获取充电时保持屏幕常亮设置
2025-08-28 19:09:08 | INFO | utils.screen_manager:get_stay_on_while_plugged_setting:175 | 充电时保持屏幕常亮设置: 所有充电方式都保持常亮 (值: 7)
2025-08-28 19:09:09 | INFO | utils.screen_manager:get_stay_on_while_plugged_setting:159 | 获取充电时保持屏幕常亮设置
2025-08-28 19:09:09 | INFO | utils.screen_manager:get_stay_on_while_plugged_setting:175 | 充电时保持屏幕常亮设置: 所有充电方式都保持常亮 (值: 7)
2025-08-28 19:09:09 | INFO | utils.screen_manager:set_stay_on_while_plugged:228 | 充电时保持屏幕常亮设置成功: 所有充电方式都保持常亮
2025-08-28 19:09:09 | INFO | testcases.test_ella.base_ella_test:setup_batch_test_screen:80 | ✅ 批量测试屏幕设置完成：屏幕永不超时，充电时保持常亮
2025-08-28 19:09:09 | INFO | utils.screen_manager:get_screen_status_info:343 | 获取屏幕状态详细信息
2025-08-28 19:09:09 | INFO | utils.screen_manager:get_current_screen_timeout:60 | 获取当前屏幕超时时间
2025-08-28 19:09:09 | INFO | utils.screen_manager:get_current_screen_timeout:68 | 当前屏幕超时时间: 2147483647ms (2147483.647秒)
2025-08-28 19:09:09 | INFO | utils.screen_manager:get_stay_on_while_plugged_setting:159 | 获取充电时保持屏幕常亮设置
2025-08-28 19:09:09 | INFO | utils.screen_manager:get_stay_on_while_plugged_setting:175 | 充电时保持屏幕常亮设置: 所有充电方式都保持常亮 (值: 7)
2025-08-28 19:09:09 | INFO | testcases.test_ella.base_ella_test:setup_batch_test_screen:84 | 📊 当前屏幕状态: 超时=永不超时, 充电常亮=所有充电方式都保持常亮
2025-08-28 19:09:09 | INFO | core.base_page:__init__:38 | 初始化页面: ella - dialogue_page
2025-08-28 19:09:09 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:299 | 🧹 开始清除手机上所有运行中的应用进程...
2025-08-28 19:09:09 | INFO | tools.adb_process_monitor:clear_all_running_processes:1825 | 🧹 开始清除手机上所有运行中的应用进程...
2025-08-28 19:09:09 | INFO | tools.adb_process_monitor:clear_all_running_processes:1841 | ⚡ 优先使用命令直接清理...
2025-08-28 19:09:11 | INFO | tools.adb_process_monitor:clear_all_running_processes:1847 | 💪 强制停止顽固应用...
2025-08-28 19:09:16 | INFO | tools.adb_process_monitor:clear_all_running_processes:1857 | 🎉 应用进程清理完成，共清理 31 个应用
2025-08-28 19:09:18 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:301 | ✅ 应用进程清理完成
2025-08-28 19:09:18 | INFO | pages.apps.ella.dialogue_page:start_app:215 | 启动Ella应用
2025-08-28 19:09:18 | INFO | pages.apps.ella.dialogue_page:start_app:223 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-28 19:09:21 | INFO | pages.apps.ella.dialogue_page:_check_app_started:281 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-08-28 19:09:21 | INFO | pages.apps.ella.dialogue_page:start_app:228 | ✅ Ella应用启动成功（指定Activity）
2025-08-28 19:09:21 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:302 | 等待Ella页面加载完成 (超时: 15秒)
2025-08-28 19:09:22 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:312 | ✅ 确认当前在Ella应用中
2025-08-28 19:09:22 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:321 | ✅ 主输入框已出现，页面加载完成
2025-08-28 19:09:22 | INFO | testcases.test_ella.base_ella_test:ella_app:333 | ✅ Ella应用启动成功
2025-08-28 19:09:22 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:371 | 初始状态None- 使用命令why my charging is so slow，状态: 
2025-08-28 19:09:22 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:770 | 确保在对话页面...
2025-08-28 19:09:22 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-08-28 19:09:22 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-08-28 19:09:22 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-28 19:09:22 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-08-28 19:09:22 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:781 | ✅ 已在对话页面
2025-08-28 19:09:22 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-08-28 19:09:22 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-08-28 19:09:22 | INFO | testcases.test_ella.base_ella_test:_execute_command:884 | 🎤 执行语音命令: why my charging is so slow (语言: en-US, 持续时间: 3.0秒)
2025-08-28 19:09:22 | INFO | pages.apps.ella.ella_command_executor:execute_voice_command:73 | 🎤 执行语音命令: 'why my charging is so slow' (语言: en-US, 持续时间: 3.0秒)
2025-08-28 19:09:22 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-08-28 19:09:23 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-08-28 19:09:23 | INFO | pages.apps.ella.ella_command_executor:_start_voice_input:342 | 🎤 启动语音输入...
2025-08-28 19:09:23 | INFO | pages.apps.ella.ella_command_executor:_try_click_voice_button_strategies:364 | 尝试多种策略点击语音按钮...
2025-08-28 19:09:23 | INFO | pages.apps.ella.ella_command_executor:_try_primary_voice_button:397 | ✅ 找到主要语音按钮，尝试点击
2025-08-28 19:09:23 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音输入按钮], 超时时间: 5秒
2025-08-28 19:09:23 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-28 19:09:23 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音输入按钮]
2025-08-28 19:09:23 | INFO | core.base_element:click:231 | 点击元素成功 [语音输入按钮]
2025-08-28 19:09:24 | INFO | pages.apps.ella.ella_command_executor:_verify_voice_input_started:541 | 验证语音输入启动状态...
2025-08-28 19:09:26 | INFO | pages.apps.ella.ella_command_executor:_verify_voice_input_started:553 | ✅ 检测到语音按钮状态变化
2025-08-28 19:09:26 | INFO | pages.apps.ella.ella_command_executor:_start_voice_input:348 | ✅ 语音输入启动成功
2025-08-28 19:09:26 | INFO | pages.apps.ella.ella_command_executor:execute_voice_command:90 | 等待语音录制状态稳定...
2025-08-28 19:09:27 | INFO | pages.apps.ella.ella_command_executor:execute_voice_command:94 | 🔊 播放语音命令: 'why my charging is so slow'
2025-08-28 19:09:27 | INFO | pages.apps.ella.ella_command_executor:play_voice_command_file:837 | 🎵 播放命令语音: 'why my charging is so slow' (语言: en-US)
2025-08-28 19:09:27 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-08-28 19:09:27 | INFO | pages.apps.ella.ella_command_executor:play_voice_command_file:857 | ✅ 找到现有音频文件: data\tts\en\why_my_charging_is_so_slow.wav
2025-08-28 19:09:27 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-08-28 19:09:27 | INFO | pages.apps.ella.ella_command_executor:_play_with_windows_player:911 | 🔊 使用Windows播放器播放: why_my_charging_is_so_slow.wav (16.3KB)
2025-08-28 19:09:28 | INFO | pages.apps.ella.ella_command_executor:_play_with_windows_player:926 | ✅ Windows播放器播放完成
2025-08-28 19:09:31 | INFO | pages.apps.ella.ella_command_executor:_stop_voice_input:606 | 🛑 停止语音输入...
2025-08-28 19:09:31 | INFO | pages.apps.ella.ella_command_executor:_try_stop_voice_input_strategies:628 | 尝试多种策略停止语音输入...
2025-08-28 19:09:32 | INFO | pages.apps.ella.ella_command_executor:_try_stop_by_coordinates:746 | 尝试点击坐标 (972, 2192) 停止语音输入
2025-08-28 19:09:33 | INFO | pages.apps.ella.ella_command_executor:_verify_voice_input_stopped:776 | 验证语音输入停止状态...
2025-08-28 19:09:34 | INFO | pages.apps.ella.ella_command_executor:_verify_voice_input_stopped:783 | ✅ 录音指示器已消失
2025-08-28 19:09:34 | INFO | pages.apps.ella.ella_command_executor:_stop_voice_input:612 | ✅ 语音输入停止成功
2025-08-28 19:09:34 | INFO | pages.apps.ella.ella_command_executor:execute_voice_command:110 | ✅ 语音命令执行完成
2025-08-28 19:09:34 | INFO | testcases.test_ella.base_ella_test:_execute_command:891 | ✅ 成功执行命令: why my charging is so slow (语音模式: True)
2025-08-28 19:09:34 | INFO | testcases.test_ella.base_ella_test:_handle_popup_after_command:178 | handle_popup_after_command:处理弹窗
2025-08-28 19:09:34 | INFO | core.popup_tool:detect_and_close_popup_once:738 | 执行单次弹窗检测和关闭
2025-08-28 19:09:40 | INFO | core.popup_tool:detect_and_close_popup_once:742 | 未检测到弹窗，无需处理
2025-08-28 19:09:40 | INFO | testcases.test_ella.base_ella_test:_get_response_timeout:1320 | 📝 使用默认等待时间: 8秒
2025-08-28 19:09:40 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:35 | 等待AI响应，超时时间: 8秒
2025-08-28 19:09:41 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:60 | ✅ 通过TTS按钮检测到响应
2025-08-28 19:09:44 | INFO | testcases.test_ella.base_ella_test:_get_final_status_with_page_info:445 | 状态检查时当前应用包名: com.transsion.aivoiceassistant
2025-08-28 19:09:44 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:563 | 状态检查完成，现在获取响应文本
2025-08-28 19:09:44 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:567 | 第1次尝试确保在Ella页面以获取响应
2025-08-28 19:09:44 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:770 | 确保在对话页面...
2025-08-28 19:09:44 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-08-28 19:09:45 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-08-28 19:09:45 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-28 19:09:45 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-08-28 19:09:45 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:781 | ✅ 已在对话页面
2025-08-28 19:09:45 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:571 | ✅ 已确认在Ella对话页面，可以获取响应
2025-08-28 19:09:45 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-08-28 19:09:45 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-08-28 19:09:45 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-28 19:09:45 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-08-28 19:09:45 | INFO | pages.apps.ella.ella_response_handler:_ensure_on_ella_page:1335 | 检查是否在Ella页面...
2025-08-28 19:09:45 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-08-28 19:09:45 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-08-28 19:09:45 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-28 19:09:45 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-08-28 19:09:45 | INFO | pages.apps.ella.ella_response_handler:_ensure_on_ella_page:1344 | ✅ 当前在Ella页面
2025-08-28 19:09:45 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:140 | 获取AI响应文本
2025-08-28 19:09:47 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | asr_txt节点不存在，已达到最大重试次数
2025-08-28 19:09:47 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:474 | ✅ 从robot_text成功获取响应: Here's the information I found for you
2025-08-28 19:09:48 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | function_name节点不存在，已达到最大重试次数
2025-08-28 19:09:50 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | function_control节点不存在，已达到最大重试次数
2025-08-28 19:09:51 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | tv_card_chat_gpt节点不存在，已达到最大重试次数
2025-08-28 19:09:52 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | tv_top节点不存在，已达到最大重试次数
2025-08-28 19:09:52 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:474 | ✅ 从tv_banner成功获取响应: Intelligent Customer Service
2025-08-28 19:09:53 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:474 | ✅ 从tv_text成功获取响应: 1. Make sure you're using the original charger, try other original charger to see whether problem comes from your charger.

2. Check your charging port to see whether it's get stuck by dirt or other foreign objectives.

3. System runs slowly because of too much system cache after using Android Phone for a long time, suggest to reset factory or software upgrade. 

4. Charging slowly with battery, suggest change new battery.

5. If nothing's wrong with the charging or the port, then try to visit local customer service center for further check.
2025-08-28 19:09:54 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | alarm_time_tv节点不存在，已达到最大重试次数
2025-08-28 19:09:55 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | alarm_day_tv节点不存在，已达到最大重试次数
2025-08-28 19:09:56 | WARNING | pages.apps.ella.ella_response_handler:_get_element_checked_with_retry:530 | alarm_switch节点不存在，已达到最大重试次数
2025-08-28 19:09:56 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:601 | 最终获取的AI响应: '['', "Here's the information I found for you", '', '', '', '', 'Intelligent Customer Service', "1. Make sure you're using the original charger, try other original charger to see whether problem comes from your charger.\n\n2. Check your charging port to see whether it's get stuck by dirt or other foreign objectives.\n\n3. System runs slowly because of too much system cache after using Android Phone for a long time, suggest to reset factory or software upgrade. \n\n4. Charging slowly with battery, suggest change new battery.\n\n5. If nothing's wrong with the charging or the port, then try to visit local customer service center for further check.", '', '', '']'
2025-08-28 19:09:57 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaWhyMyChargingIsSoSlowVoice\test_completed.png
2025-08-28 19:09:57 | INFO | testcases.test_ella.base_ella_test:simple_command_test:1401 | 🎉 why my charging is so slow 测试完成
2025-08-28 19:09:57 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:1111 | verify_expected_in_response_advanced 响应类型: <class 'list'>, 搜索模式: combined, 匹配模式: 任意匹配
2025-08-28 19:09:57 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:1179 | ⚠️ 未找到期望内容: 'These suggestions are for your reference'
2025-08-28 19:09:57 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:1154 | ✅ [合并模式] 找到期望内容: 'the information I found for you'
2025-08-28 19:09:57 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:1175 | 🎉 [任意匹配模式] 找到期望内容，验证通过: 'the information I found for you'
2025-08-28 19:09:57 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaWhyMyChargingIsSoSlowVoice\test_completed.png
2025-08-28 19:09:57 | INFO | pages.apps.ella.dialogue_page:stop_app:389 | 停止Ella应用
2025-08-28 19:09:59 | INFO | pages.apps.ella.dialogue_page:stop_app:400 | ✅ Ella应用已成功停止
2025-08-28 19:09:59 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-08-28 19:09:59 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-08-28 19:09:59 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
