{"name": "测试who is harry potter能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含[' harry potter']，实际响应: '['What is the weather', 'Sorry, weather data error, try later.', '', '', '', '', '', '', '', '', '']'\nassert False", "trace": "self = <test_voice_who_is_harry_potter.TestEllaWhoIsHarryPotterVoice object at 0x000001ED89BFF4D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001ED89CB9D90>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_who_is_harry_potter_voice(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        voice_language = self.voice_language\n        voice_duration = self.voice_duration\n    \n        with allure.step(f\"执行语音命令: {command} (语言: {voice_language})\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command,\n                is_voice=True,  # 语音标识为True\n                voice_duration=voice_duration,\n                voice_language=voice_language, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含[' harry potter']，实际响应: '['What is the weather', 'Sorry, weather data error, try later.', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_voice_ella\\dialogue\\test_voice_who_is_harry_potter.py:40: AssertionError"}, "description": "who is harry potter", "steps": [{"name": "执行语音命令: who is harry potter (语言: en-US)", "status": "passed", "steps": [{"name": "执行命令: who is harry potter", "status": "passed", "start": 1756379252530, "stop": 1756379289584}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "866ed508-ffb9-4f19-b7e7-e161d898435f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "83aa0a37-eebe-40be-bf67-0bce11531048-attachment.png", "type": "image/png"}], "start": 1756379289584, "stop": 1756379290046}], "start": 1756379252530, "stop": 1756379290047}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含[' harry potter']，实际响应: '['What is the weather', 'Sorry, weather data error, try later.', '', '', '', '', '', '', '', '', '']'\nassert False\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_voice_ella\\dialogue\\test_voice_who_is_harry_potter.py\", line 40, in test_who_is_harry_potter_voice\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756379290047, "stop": 1756379290058}], "attachments": [{"name": "stdout", "source": "2c464c2b-01bc-43db-a732-9a6e20293851-attachment.txt", "type": "text/plain"}], "start": 1756379252530, "stop": 1756379290060, "uuid": "84c19b74-a732-4780-9be0-927150dc9b72", "historyId": "6eadaf8d76477bf232deee7a353f810b", "testCaseId": "6eadaf8d76477bf232deee7a353f810b", "fullName": "testcases.test_voice_ella.dialogue.test_voice_who_is_harry_potter.TestEllaWhoIsHarryPotterVoice#test_who_is_harry_potter_voice", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_voice_ella.dialogue"}, {"name": "suite", "value": "test_voice_who_is_harry_potter"}, {"name": "subSuite", "value": "TestEllaWhoIsHarryPotterVoice"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "26792-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_voice_ella.dialogue.test_voice_who_is_harry_potter"}]}