"""
Ella语音助手基础指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("打开应用")
class TestEllaOpenPlayPoliticalNews(SimpleEllaTest):
    """Ella打开make a phone call to 17621905233测试类"""

    @allure.title("测试make a phone call to 17621905233")
    @allure.description("测试make a phone call to 17621905233指令")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    @pytest.mark.skip(reason="make a phone call to 17621905233命令需要先添加联系人，才能拨打电话，无法通用化")
    def test_make_a_phone_call_to_17621905233(self, ella_app):
        """测试make a phone call to 17621905233命令"""
        command = "make a phone call to 17621905233"
        app_name = 'contacts'

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command
            )

        with allure.step("验证响应包含期望内容"):
            expected_text = []
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"

        with allure.step(f"验证{app_name}已打开"):
            assert final_status, f"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
